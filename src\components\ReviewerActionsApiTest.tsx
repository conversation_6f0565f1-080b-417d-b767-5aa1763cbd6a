import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ApiService } from '@/lib/utils';
import { toast } from 'sonner';

const ReviewerActionsApiTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [actions, setActions] = useState<any>(null);

  const testReviewerActionsEndpoint = async () => {
    setIsLoading(true);
    setError(null);
    setActions(null);

    try {
      console.log('🔄 Testing /actions/INCIDENT endpoint...');
      const response = await ApiService.getReviewerActions();
      console.log('✅ Reviewer actions test successful:', response);
      setActions(response);
      toast.success('Reviewer actions loaded successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Reviewer actions test failed:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load reviewer actions: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Reviewer Actions API Test (/actions/INCIDENT)
          <Button
            onClick={testReviewerActionsEndpoint}
            disabled={isLoading}
            variant="outline"
          >
            {isLoading ? 'Loading...' : 'Test /actions/INCIDENT Endpoint'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Endpoint:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block">
            GET https://dev.stt-user.acuizen.com/actions/INCIDENT
          </code>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded p-4">
            <h3 className="font-medium text-red-800 mb-2">Error:</h3>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {actions && (
          <div className="bg-green-50 border border-green-200 rounded p-4">
            <h3 className="font-medium text-green-800 mb-2">Success! Response:</h3>
            <div className="text-sm text-green-700">
              <p className="mb-2">
                <strong>Type:</strong> {Array.isArray(actions) ? 'Array' : typeof actions}
              </p>
              {Array.isArray(actions) && (
                <p className="mb-2">
                  <strong>Count:</strong> {actions.length} incidents
                </p>
              )}
              <details className="mt-2">
                <summary className="cursor-pointer font-medium">View Raw Response</summary>
                <pre className="mt-2 bg-white p-2 rounded border text-xs overflow-auto max-h-96">
                  {JSON.stringify(actions, null, 2)}
                </pre>
              </details>
            </div>
          </div>
        )}

        <div className="text-sm text-gray-600">
          <h3 className="font-medium mb-2">Expected Response:</h3>
          <ul className="list-disc list-inside space-y-1">
            <li>Array of incident objects requiring reviewer action</li>
            <li>Each incident should have fields like id, title, status, etc.</li>
            <li>Should include incident details for reviewer actions</li>
            <li>API accepts no parameters - returns all reviewer actions</li>
            <li>Should be called only once to prevent repeated API calls</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReviewerActionsApiTest;
