import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ApiService } from '@/lib/utils';
import { toast } from 'sonner';

const ReviewerActionsApiTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [actions, setActions] = useState<any>(null);
  const [submittedBy, setSubmittedBy] = useState('');

  const testReviewerActionsEndpoint = async () => {
    setIsLoading(true);
    setError(null);
    setActions(null);

    try {
      console.log('🔄 Testing /actions endpoint...');
      console.log('🔧 Using submittedBy:', submittedBy || 'none');
      const response = await ApiService.getReviewerActions(submittedBy || undefined);
      console.log('✅ Reviewer actions test successful:', response);
      setActions(response);
      toast.success('Reviewer actions loaded successfully!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('❌ Reviewer actions test failed:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load reviewer actions: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Reviewer Actions API Test (/actions)
          <Button 
            onClick={testReviewerActionsEndpoint} 
            disabled={isLoading}
            variant="outline"
          >
            {isLoading ? 'Loading...' : 'Test /actions Endpoint'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Test Parameters:</h3>
          <div className="flex gap-2 items-end">
            <div className="flex-1">
              <label className="text-sm font-medium mb-1 block">submittedBy (optional):</label>
              <Input
                value={submittedBy}
                onChange={(e) => setSubmittedBy(e.target.value)}
                placeholder="Enter username to filter by submittedBy"
                className="text-sm"
              />
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-2">Endpoint:</h3>
          <code className="bg-gray-100 p-2 rounded text-sm block">
            GET https://dev.stt-user.acuizen.com/actions{submittedBy ? `?submittedBy=${submittedBy}` : ''}
          </code>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded p-4">
            <h3 className="font-medium text-red-800 mb-2">Error:</h3>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {actions && (
          <div className="bg-green-50 border border-green-200 rounded p-4">
            <h3 className="font-medium text-green-800 mb-2">Success! Response:</h3>
            <div className="text-sm text-green-700">
              <p className="mb-2">
                <strong>Type:</strong> {Array.isArray(actions) ? 'Array' : typeof actions}
              </p>
              {Array.isArray(actions) && (
                <p className="mb-2">
                  <strong>Count:</strong> {actions.length} incidents
                </p>
              )}
              <details className="mt-2">
                <summary className="cursor-pointer font-medium">View Raw Response</summary>
                <pre className="mt-2 bg-white p-2 rounded border text-xs overflow-auto max-h-96">
                  {JSON.stringify(actions, null, 2)}
                </pre>
              </details>
            </div>
          </div>
        )}

        <div className="text-sm text-gray-600">
          <h3 className="font-medium mb-2">Expected Response:</h3>
          <ul className="list-disc list-inside space-y-1">
            <li>Array of incident objects requiring reviewer action</li>
            <li>Each incident should have fields like id, title, status, etc.</li>
            <li>If submittedBy is provided, incidents should be filtered by that user</li>
            <li>Should include incident details without complex filters</li>
            <li>API only accepts submittedBy parameter, no other filters</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReviewerActionsApiTest;
