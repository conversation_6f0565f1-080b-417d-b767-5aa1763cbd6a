import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { mockIncidents } from '@/utils/mockData';
import { ApiService } from '@/lib/utils';

// Define the incident type
export interface Incident {
  id: string;
  maskId?: string;
  classification?: string;
  incidentTitle?: string;
  incidentDate: Date;
  incidentTime: string;
  incidentType: string;
  incidentCategory: string;
  description: string;

  // Location information
  locationCountry: string;
  locationCity: string;
  locationBusinessUnit: string;
  locationProject: string;
  locationDetails: string;

  // Location IDs for reference
  locationOneId?: string;
  locationTwoId?: string;
  locationThreeId?: string;
  locationFourId?: string;
  locationFiveId?: string;
  locationSixId?: string;

  // Classification
  isWorkRelated: boolean | null;
  lossOfConsciousness: boolean | null;
  isDangerousOccurrence: boolean | null;
  injuryClassification: {
    isFatality: boolean | null;
    isPermanentDisability: boolean | null;
    isLostTimeIncident: boolean | null;
    isMedicalTreatment: boolean | null;
    isFirstAid: boolean | null;
  };
  environmentalClassification?: string;
  propertyDamageClassification?: string;

  // Incident management
  reportedAt: Date;
  reportedBy: string;
  reviewedBy: string | null;

  requiresAction?: boolean;
  status: 'draft' | 'submitted' | 'under-review' | 'investigation' | 'closed' | 'reported' | 'Reported';
  stage?: string; // Stage of the incident (e.g., "Supplementary information in Progress")
  workflowStage?: 'initial' | 'review' | 'investigation'; // Workflow stage for tracking progress
  investigationStatus: 'not-started' | 'in-progress' | 'completed';

  // Investigation assignment
  leadInvestigator?: string;
  investigationRemarks?: string;

  // Additional details
  propertyDamage: boolean;
  propertyDamageDetails: string;
  riskCategories: string[];
  photos: string[];
  attachments: string[];

  // Action dialog fields
  immediateActionDate?: string;
  immediateActionsTaken?: string;
  legalClassification?: string;
  workplaceActivity?: string;
  circumstances?: string;

  // Investigation details
  investigationDetails: {
    rootCause?: string;
    correctiveActions?: string;
    preventiveMeasures?: string;
    assignedTo?: string;
    completionDate?: Date;

    // Incident Details
    incidentLocation?: string;
    locationPlanPhotos?: string[];

    // People Involved
    peopleInjured?: string;
    witnesses?: string;

    // Description of Incident
    howItHappened?: string;
    equipmentInvolved?: string;
    activitiesPerformed?: string;
    unusualConditions?: string;
    safeWorkingProcedures?: string;

    // Injury Details
    injurySustained?: boolean;
    bodyPartsInjured?: string;
    injuryDescription?: string;
    firstAidAdministered?: boolean;
    firstAidAdministeredBy?: string;
    takenToMedicalFacility?: boolean;
    medicalFacilityName?: string;
    transportMode?: string[];
    driverInfo?: string;
    medicalTreatmentReceived?: boolean;
    treatmentDescription?: string;
    medicalLeaveGiven?: boolean;
    medicalLeaveDays?: number;

    // Return to Work Information
    regularWorkReturnDate?: Date;
    modifiedWorkReturnDate?: Date;
  } | null;

  // Comprehensive Investigation Details (after lead investigator assignment)
  comprehensiveInvestigation?: {
    // Investigation Team
    investigationTeam?: {
      leadInvestigator?: string;
      teamMembers?: string[];
      investigationStartDate?: Date;
      investigationEndDate?: Date;
    };

    // Sequence of Events
    sequenceOfEvents?: {
      eventDescription?: string;
      timeline?: Array<{
        time: string;
        event: string;
        evidence?: string;
      }>;
      contributingFactors?: string;
    };

    // Information Gathering - People
    informationGatheringPeople?: {
      witnessInterviews?: Array<{
        name: string;
        role: string;
        statement: string;
        dateInterviewed: Date;
      }>;
      injuredPersonStatement?: string;
      supervisorStatement?: string;
      otherPersonnelInvolved?: string;
    };

    // Information Gathering - Equipment
    informationGatheringEquipment?: {
      equipmentInvolved?: string;
      equipmentCondition?: string;
      maintenanceRecords?: string;
      inspectionRecords?: string;
      equipmentDefects?: string;
      equipmentPhotos?: string[];
    };

    // Information Gathering - Material
    informationGatheringMaterial?: {
      materialsInvolved?: string;
      materialCondition?: string;
      materialSpecifications?: string;
      materialSafetyDataSheets?: string;
      materialHandlingProcedures?: string;
      materialDefects?: string;
    };

    // Information Gathering - Environment
    informationGatheringEnvironment?: {
      weatherConditions?: string;
      lightingConditions?: string;
      noiseLevel?: string;
      temperatureConditions?: string;
      workspaceLayout?: string;
      housekeepingConditions?: string;
      environmentalHazards?: string;
    };

    // Information Gathering - Method
    informationGatheringMethod?: {
      workProcedures?: string;
      trainingRecords?: string;
      safetyProcedures?: string;
      riskAssessments?: string;
      permitToWork?: string;
      supervisionLevel?: string;
      communicationMethods?: string;
    };

    // Conclusions
    investigationConclusions?: {
      rootCauses?: Array<{
        category: string;
        description: string;
        evidence: string;
      }>;
      contributingFactors?: Array<{
        factor: string;
        impact: string;
      }>;
      lessonsLearned?: string;
      recommendations?: Array<{
        recommendation: string;
        priority: 'High' | 'Medium' | 'Low';
        targetDate: Date;
        responsiblePerson: string;
      }>;
    };

    // Action Controls
    actionControls?: Array<{
      actionType: 'Immediate' | 'Short-term' | 'Long-term';
      description: string;
      responsiblePerson: string;
      targetDate: Date;
      status: 'Pending' | 'In Progress' | 'Completed';
      verificationMethod: string;
    }>;
  };
}

// Define the context type
interface IncidentContextType {
  incidents: Incident[];
  isLoading: boolean;
  error: string | null;
  addIncident: (incident: Incident) => void;
  updateIncident: (id: string, updatedIncident: Partial<Incident>) => void;
  deleteIncident: (id: string) => void;
  getIncidentById: (id: string) => Incident | undefined;
  getIncidentsByReporter: (reporterName: string) => Incident[];
  getIncidentsByStatus: (status: Incident['status']) => Incident[];
  getIncidentsRequiringAction: (userName: string, role: 'reporter' | 'reviewer') => Incident[];
  getReporterActionIncidents: () => Promise<Incident[]>;
  getReviewerActionIncidents: () => Promise<Incident[]>;
  refreshIncidents: () => Promise<void>;
}

// Create the context with default values
const IncidentContext = createContext<IncidentContextType>({
  incidents: [],
  isLoading: false,
  error: null,
  addIncident: () => {},
  updateIncident: () => {},
  deleteIncident: () => {},
  getIncidentById: () => undefined,
  getIncidentsByReporter: () => [],
  getIncidentsByStatus: () => [],
  getIncidentsRequiringAction: () => [],
  getReporterActionIncidents: async () => [],
  getReviewerActionIncidents: async () => [],
  refreshIncidents: async () => {},
});

// Create a provider component
interface IncidentProviderProps {
  children: ReactNode;
}

export const IncidentProvider: React.FC<IncidentProviderProps> = ({ children }) => {
  // State management
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Function to load incidents from API
  const refreshIncidents = async () => {
    setIsLoading(true);
    setError(null);
    try {
      console.log("🔄 IncidentContext: Loading incidents from API with version filter...");
      const response = await ApiService.getIncidents();
      console.log("✅ IncidentContext: Incidents loaded from API:", response);

      // Log version information for debugging
      if (Array.isArray(response)) {
        const versionCounts = response.reduce((acc: any, incident: any) => {
          const version = incident.version || 'undefined';
          acc[version] = (acc[version] || 0) + 1;
          return acc;
        }, {});
        console.log("📊 IncidentContext: Version distribution:", versionCounts);
        console.log("🔍 IncidentContext: Total incidents received:", response.length);
      }

      // Handle different response formats
      let incidentData = response;
      if (response && response.data) {
        incidentData = response.data;
      }

      // Transform API data to match our Incident interface if needed
      if (Array.isArray(incidentData)) {
        console.log('🔄 Main API Response:', incidentData);
        console.log('🔄 First item location fields:', {
          locationCountry: incidentData[0]?.locationCountry,
          locationOne: incidentData[0]?.locationOne,
          locationTwo: incidentData[0]?.locationTwo,
          country: incidentData[0]?.country,
          city: incidentData[0]?.city,
        });

        // Filter for version "new" incidents only
        const newVersionIncidents = incidentData.filter((item: any) => item.version === "new");
        console.log(`🔍 IncidentContext: Filtered ${newVersionIncidents.length} "new" incidents from ${incidentData.length} total incidents`);

        // Map API data to our Incident interface
        const transformedIncidents = newVersionIncidents.map((item: any) => ({
          ...item,
          // Ensure required fields exist with defaults
          id: item.id || `INC-${Date.now()}`,
          maskId: item.maskId || item.id || `INC-${Date.now()}`,
          classification: item.classification || "",
          incidentDate: item.incidentDate ? new Date(item.incidentDate) : new Date(),
          incidentTime: item.incidentTime || "00:00",
          incidentType: item.incidentType || item.IncidentCategory || "unknown",
          incidentCategory: item.incidentCategory || item.IncidentCategory || "unknown",
          description: item.description || item.title || "No description",
          // Location information - map from populated objects or fallback to IDs
          // Priority: populated objects > incidentData names > direct names > IDs
          locationCountry: item.locationOne?.name || item.incidentData?.locationCountry || item.locationCountry || item.locationOneId || "",
          locationCity: item.locationTwo?.name || item.incidentData?.locationCity || item.locationCity || item.locationTwoId || "",
          locationBusinessUnit: item.locationThree?.name || item.incidentData?.locationBusinessUnit || item.locationBusinessUnit || item.locationThreeId || "",
          locationProject: item.locationFour?.name || item.incidentData?.locationProject || item.locationProject || item.locationFourId || "",
          locationDetails: item.locationFive?.name || item.locationSix?.name || item.incidentData?.locationDetails || item.locationDetails || item.locationFiveId || item.locationSixId || "",

          // Store location IDs for reference
          locationOneId: item.locationOneId || "",
          locationTwoId: item.locationTwoId || "",
          locationThreeId: item.locationThreeId || "",
          locationFourId: item.locationFourId || "",
          locationFiveId: item.locationFiveId || "",
          locationSixId: item.locationSixId || "",
          isWorkRelated: item.isWorkRelated ?? true,
          lossOfConsciousness: item.lossOfConsciousness ?? false,
          isDangerousOccurrence: item.isDangerousOccurrence ?? false,
          injuryClassification: item.injuryClassification || {
            isFatality: false,
            isPermanentDisability: false,
            isLostTimeIncident: false,
            isMedicalTreatment: false,
            isFirstAid: false,
          },
          reportedAt: item.reportedAt ? new Date(item.reportedAt) : new Date(),
          reportedBy: item.reportedBy || "Unknown",
          reviewedBy: item.reviewedBy || null,
          requiresAction: item.requiresAction ?? false,
          status: item.status || "draft",
          stage: item.stage || "Initial Report",
          investigationStatus: item.investigationStatus || "not-started",
          propertyDamage: item.propertyDamage ?? false,
          propertyDamageDetails: item.propertyDamageDetails || "",
          riskCategories: item.riskCategories || [],
          photos: item.photos || [],
          attachments: item.attachments || [],
          investigationDetails: item.investigationDetails || null,
        }));

        setIncidents(transformedIncidents);
        console.log(`✅ IncidentContext: Successfully loaded ${transformedIncidents.length} incidents from API`);
      } else {
        console.warn("⚠️ IncidentContext: API response is not an array, using mock data as fallback");
        setIncidents(mockIncidents as Incident[]);
      }
    } catch (error) {
      console.error("❌ IncidentContext: Failed to load incidents from API:", error);
      setError(error instanceof Error ? error.message : 'Failed to load incidents from API');
      // Fallback to mock data on error
      console.log("📋 IncidentContext: Using mock data as fallback");
      setIncidents(mockIncidents as Incident[]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load incidents on component mount
  useEffect(() => {
    refreshIncidents();
  }, []);

  const addIncident = (incident: Incident) => {
    setIncidents((prevIncidents) => [incident, ...prevIncidents]);
  };

  const updateIncident = (id: string, updatedIncident: Partial<Incident>) => {
    console.log(`Updating incident ${id} with:`, updatedIncident);

    setIncidents((prevIncidents) => {
      const newIncidents = prevIncidents.map((incident) => {
        if (incident.id === id) {
          // Create a deep copy of the original incident to ensure we don't lose any data
          const originalCopy = JSON.parse(JSON.stringify(incident));

          // Create a deep copy of the updated incident
          const updatedCopy = JSON.parse(JSON.stringify(updatedIncident));

          // Merge the two objects, with updated values taking precedence
          const mergedIncident = { ...originalCopy, ...updatedCopy };

          // Ensure injuryClassification is properly merged if it exists in both objects
          if (updatedIncident.injuryClassification && incident.injuryClassification) {
            mergedIncident.injuryClassification = {
              ...incident.injuryClassification,
              ...updatedIncident.injuryClassification
            };
          }

          // Note: potentiallySerious property removed as it's not in the current Incident interface

          // Ensure investigationDetails is properly merged if it exists in both objects
          if (updatedIncident.investigationDetails && incident.investigationDetails) {
            mergedIncident.investigationDetails = {
              ...incident.investigationDetails,
              ...updatedIncident.investigationDetails
            };
          }

          // Ensure photos array is properly merged without duplicates
          if (updatedIncident.photos && incident.photos) {
            // Use Set to remove duplicates
            mergedIncident.photos = [...new Set([...incident.photos, ...updatedIncident.photos])];
          }

          // Ensure attachments array is properly merged without duplicates
          if (updatedIncident.attachments && incident.attachments) {
            // Use Set to remove duplicates
            mergedIncident.attachments = [...new Set([...incident.attachments, ...updatedIncident.attachments])];
          }

          // Log the updated incident for debugging
          console.log(`Updated incident ${id}:`, mergedIncident);

          return mergedIncident;
        }
        return incident;
      });

      return newIncidents;
    });
  };

  const deleteIncident = (id: string) => {
    setIncidents((prevIncidents) =>
      prevIncidents.filter((incident) => incident.id !== id)
    );
  };

  const getIncidentById = (id: string) => {
    return incidents.find((incident) => incident.id === id);
  };

  const getIncidentsByReporter = (reporterName: string) => {
    return incidents.filter((incident) => incident.reportedBy === reporterName);
  };

  const getIncidentsByStatus = (status: Incident['status']) => {
    return incidents.filter((incident) => incident.status === status);
  };

  const getIncidentsRequiringAction = (userName: string, role: 'reporter' | 'reviewer') => {
    console.log(`Getting incidents for ${role} with userName: ${userName}`);

    if (role === 'reporter') {
      // For reporters, show:
      // 1. Incidents they reported that are in under-review status and require action
      // 2. Incidents they reported that are in submitted status (after reviewer submission) and in 'Preliminary Analysis in Progress' stage
      const result = incidents.filter(
        (incident) =>
          incident.reportedBy === userName &&
          incident.requiresAction === true &&
          (
            // Include under-review incidents that require action
            incident.status === 'under-review' ||
            // Include submitted incidents in Preliminary Analysis stage (after reviewer submission)
            (incident.status === 'submitted' && incident.stage === 'Preliminary Analysis in Progress')
          )
      );
      console.log(`Reporter incidents requiring action: ${result.length}`);
      console.log("Reporter incidents:", result);
      return result;
    } else if (role === 'reviewer') {
      // For reviewers, show:
      // 1. All incidents that are under-review and require action
      // 2. All incidents that are in submitted status and in 'Preliminary Analysis in Progress' stage (after reviewer submission)
      const result = incidents.filter(
        (incident) =>
          incident.requiresAction === true &&
          (
            // Include under-review incidents that require action
            incident.status === 'under-review' ||
            // Include submitted incidents in Preliminary Analysis stage (after reviewer submission)
            (incident.status === 'submitted' && incident.stage === 'Preliminary Analysis in Progress')
          )
      );
      console.log(`Reviewer incidents requiring action: ${result.length}`);
      console.log("Reviewer incidents:", result);
      return result;
    }
    return [];
  };

  const getReporterActionIncidents = useCallback(async (): Promise<Incident[]> => {
    try {
      console.log('🔍 Fetching reporter action incidents from API...');
      const response = await ApiService.getReporterIncidents();

      console.log('📋 Raw API Response:', response);
      console.log('📋 Response type:', typeof response);
      console.log('📋 Is Array:', Array.isArray(response));

      if (response && Array.isArray(response)) {
        console.log('📋 First item structure:', response[0]);
        console.log('📋 Location fields in first item:', {
          locationCountry: response[0]?.locationCountry,
          locationCity: response[0]?.locationCity,
          locationOne: response[0]?.locationOne,
          locationTwo: response[0]?.locationTwo,
          locationThree: response[0]?.locationThree,
          locationFour: response[0]?.locationFour,
          locationFive: response[0]?.locationFive,
          locationSix: response[0]?.locationSix,
          locationOneId: response[0]?.locationOneId,
          locationTwoId: response[0]?.locationTwoId,
          locationThreeId: response[0]?.locationThreeId,
          locationFourId: response[0]?.locationFourId,
          locationFiveId: response[0]?.locationFiveId,
          locationSixId: response[0]?.locationSixId,
          country: response[0]?.country,
          city: response[0]?.city,
        });

        // Filter for version "new" incidents only
        const newVersionIncidents = response.filter((item: any) => item.version === "new");
        console.log(`🔍 getReporterActionIncidents: Filtered ${newVersionIncidents.length} "new" incidents from ${response.length} total incidents`);

        // Transform API data to match our Incident interface
        const transformedIncidents = newVersionIncidents.map((item: any) => ({
          ...item,
          // Ensure required fields exist with defaults
          id: item.id || `INC-${Date.now()}`,
          maskId: item.maskId || item.id || `INC-${Date.now()}`,
          classification: item.classification || "",
          incidentTitle: item.title || item.incidentData?.incidentTitle || item.incidentTitle || "Untitled Incident",
          description: item.description || item.incidentData?.description || "",
          incidentDate: item.incidentDate || item.incidentData?.incidentDate || item.date || new Date().toISOString(),
          incidentTime: item.incidentData?.incidentTime || item.incidentTime || item.time || "00:00",
          incidentType: item.incidentData?.incidentType || item.incidentType || item.IncidentCategory || "",
          incidentCategory: item.incidentData?.incidentCategory || item.incidentCategory || item.IncidentCategory || "",
          reportedBy: item.reportedBy || item.reporter || "",
          reportedAt: item.reportedAt || item.createdAt || new Date().toISOString(),
          status: item.status || "draft",
          stage: item.stage || "Initial Report",
          requiresAction: item.requiresAction !== undefined ? item.requiresAction : true,
          assignedTo: item.assignedTo || "",
          reviewedBy: item.reviewedBy || "",
          reviewedAt: item.reviewedAt || "",
          investigatedBy: item.investigatedBy || "",
          investigatedAt: item.investigatedAt || "",

          // Location information - map from populated objects or fallback to IDs
          // Priority: populated objects > incidentData names > direct names > IDs
          locationCountry: item.locationOne?.name || item.incidentData?.locationCountry || item.locationCountry || item.locationOneId || "",
          locationCity: item.locationTwo?.name || item.incidentData?.locationCity || item.locationCity || item.locationTwoId || "",
          locationBusinessUnit: item.locationThree?.name || item.incidentData?.locationBusinessUnit || item.locationBusinessUnit || item.locationThreeId || "",
          locationProject: item.locationFour?.name || item.incidentData?.locationProject || item.locationProject || item.locationFourId || "",
          locationDetails: item.locationFive?.name || item.locationSix?.name || item.incidentData?.locationDetails || item.locationDetails || item.locationFiveId || item.locationSixId || "",

          // Store location IDs for reference
          locationOneId: item.locationOneId || "",
          locationTwoId: item.locationTwoId || "",
          locationThreeId: item.locationThreeId || "",
          locationFourId: item.locationFourId || "",
          locationFiveId: item.locationFiveId || "",
          locationSixId: item.locationSixId || "",

          // Classification fields - map from API response structure
          isWorkRelated: item.isWorkRelated !== undefined ? item.isWorkRelated : (item.incidentData?.isWorkRelated !== undefined ? item.incidentData.isWorkRelated : null),
          lossOfConsciousness: item.lossOfConscious === "true" ? true : (item.lossOfConscious === "false" ? false : (item.incidentData?.lossOfConsciousness !== undefined ? item.incidentData.lossOfConsciousness : null)),
          isDangerousOccurrence: item.dangerousOccurance === "true" ? true : (item.dangerousOccurance === "false" ? false : (item.incidentData?.isDangerousOccurrence !== undefined ? item.incidentData.isDangerousOccurrence : null)),
          injuryClassification: item.incidentData?.injuryClassification || {
            isFatality: item.fatality === "true" ? true : (item.fatality === "false" ? false : null),
            isPermanentDisability: item.incidentData?.injuryClassification?.isPermanentDisability || null,
            isLostTimeIncident: item.lostTime === "true" ? true : (item.lostTime === "false" ? false : null),
            isMedicalTreatment: item.medicalTreatment === "true" ? true : (item.medicalTreatment === "false" ? false : null),
            isFirstAid: item.firstAid === "true" ? true : (item.firstAid === "false" ? false : null),
          },

          impactClassification: item.impactClassification || "",
          attachments: item.attachments || [],
          actionsTaken: item.actionsTaken || "",
          investigationStatus: item.investigationStatus || "not-started",

          // Additional fields for action dialog
          immediateActionDate: item.immediateActionDate || "",
          immediateActionsTaken: item.immediateActionsTaken || "",
          legalClassification: item.legalClassification || "",
          workplaceActivity: item.incidentData?.workplaceActivity || item.workplaceActivity || "",
          circumstances: item.incidentData?.circumstances || item.circumstances || "",

          // Additional API fields
          propertyDamage: item.propertyDamage === "true" ? true : (item.propertyDamage === "false" ? false : (item.incidentData?.propertyDamage || false)),
          propertyDamageDetails: item.propertyDamageDetails || item.incidentData?.propertyDamageDetails || "",
        }));

        // Filter to only show incidents with "Reported" status for My Actions
        const reportedIncidents = transformedIncidents.filter(incident =>
          incident.status === "Reported" || incident.status === "reported"
        );

        console.log(`✅ Successfully fetched ${transformedIncidents.length} total incidents, ${reportedIncidents.length} with "Reported" status`);
        console.log('🎯 Sample transformed incident:', reportedIncidents[0]);
        console.log('🎯 Location data in transformed incident:', {
          locationCountry: reportedIncidents[0]?.locationCountry,
          locationCity: reportedIncidents[0]?.locationCity,
          locationBusinessUnit: reportedIncidents[0]?.locationBusinessUnit,
          locationProject: reportedIncidents[0]?.locationProject,
          locationDetails: reportedIncidents[0]?.locationDetails,
        });
        return reportedIncidents;
      } else {
        console.log('⚠️ API response is not an array, returning empty array');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching reporter action incidents:', error);
      return [];
    }
  }, []);

  const getReviewerActionIncidents = useCallback(async (): Promise<Incident[]> => {
    try {
      console.log('🔍 Fetching reviewer action incidents from /actions/get/INCIDENT API...');
      const response = await ApiService.getReviewerActions();

      console.log('📋 Raw API Response:', response);
      console.log('📋 Response type:', typeof response);
      console.log('📋 Is Array:', Array.isArray(response));

      if (Array.isArray(response)) {
        // Log version information for debugging
        const versionCounts = response.reduce((acc: Record<string, number>, incident: any) => {
          const version = incident.version || 'undefined';
          acc[version] = (acc[version] || 0) + 1;
          return acc;
        }, {});
        console.log('📊 Version distribution:', versionCounts);

        // Filter for version "new" incidents only, but if no "new" incidents, use all
        const newVersionIncidents = response.filter((item: any) => item.version === "new");
        console.log(`🔍 IncidentContext: Filtered ${newVersionIncidents.length} "new" incidents from ${response.length} total incidents`);

        // If no "new" version incidents found, use all incidents (for /actions/get/INCIDENT endpoint)
        const incidentsToProcess = newVersionIncidents.length > 0 ? newVersionIncidents : response;
        console.log(`🔍 IncidentContext: Processing ${incidentsToProcess.length} incidents (${newVersionIncidents.length > 0 ? 'filtered' : 'all'})`);

        // Map API data to our Incident interface
        const transformedIncidents = incidentsToProcess.map((item: any) => ({
          ...item,
          // Ensure required fields exist with defaults
          id: item.id || `INC-${Date.now()}`,
          maskId: item.maskId || item.id || `INC-${Date.now()}`,
          title: item.title || item.incidentTitle || 'Untitled Incident',
          description: item.description || item.incidentDescription || '',
          status: item.status || 'reported',
          priority: item.priority || 'medium',
          reportedBy: item.reportedBy || item.user?.firstName || 'Unknown',
          reportedAt: item.reportedAt || item.createdAt || new Date().toISOString(),
          location: item.location || 'Unknown Location',
          category: item.category || item.incidentCircumstanceCategory?.name || 'Unknown',
          classification: item.classification || 'Unclassified',
          stage: item.stage || 'Initial Report',
          workflowStage: item.workflowStage || 'reported',
          requiresAction: item.requiresAction !== undefined ? item.requiresAction : true,
          version: item.version || 'new'
        }));

        console.log(`✅ Transformed ${transformedIncidents.length} reviewer action incidents`);
        return transformedIncidents;
      } else {
        console.log('⚠️ API response is not an array, returning empty array');
        return [];
      }
    } catch (error) {
      console.error('❌ Error fetching reviewer action incidents:', error);
      return [];
    }
  }, []);

  return (
    <IncidentContext.Provider
      value={{
        incidents,
        isLoading,
        error,
        addIncident,
        updateIncident,
        deleteIncident,
        getIncidentById,
        getIncidentsByReporter,
        getIncidentsByStatus,
        getIncidentsRequiringAction,
        getReporterActionIncidents,
        getReviewerActionIncidents,
        refreshIncidents,
      }}
    >
      {children}
    </IncidentContext.Provider>
  );
};

// Create a hook to use the incident context
export const useIncidents = () => useContext(IncidentContext);
